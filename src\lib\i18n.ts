import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Language configuration
export const languages = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'nl', name: 'Dutch', nativeName: 'Nederlands' },
  { code: 'fr', name: 'French', nativeName: 'Français' },
];

export const defaultLanguage = 'en';
export const supportedLanguages = languages.map(lang => lang.code);

i18n
  // Load translations using http backend
  .use(Backend)
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    // Default language
    lng: defaultLanguage,
    fallbackLng: defaultLanguage,
    
    // Supported languages
    supportedLngs: supportedLanguages,
    
    // Debug mode (set to false in production)
    debug: import.meta.env.DEV,
    
    // Language detection options
    detection: {
      // Order of language detection methods
      order: ['localStorage', 'sessionStorage', 'navigator', 'htmlTag'],
      
      // Cache user language
      caches: ['localStorage', 'sessionStorage'],
      
      // Optional: exclude certain languages from detection
      excludeCacheFor: ['cimode'],
      
      // Key to store language in localStorage/sessionStorage
      lookupLocalStorage: 'i18nextLng',
      lookupSessionStorage: 'i18nextLng',
    },
    
    // Backend options for loading translations
    backend: {
      // Path to load resources from
      loadPath: '/locales/{{lng}}/{{ns}}.json',
      
      // Allow cross domain requests
      crossDomain: false,
      
      // Request timeout
      requestOptions: {
        cache: 'default',
      },
    },
    
    // Namespace configuration
    ns: ['common', 'pages'],
    defaultNS: 'common',
    
    // Interpolation options
    interpolation: {
      // React already escapes values
      escapeValue: false,
      
      // Format function for custom formatting
      format: (value, format, lng) => {
        if (format === 'uppercase') return value.toUpperCase();
        if (format === 'lowercase') return value.toLowerCase();
        if (format === 'capitalize') return value.charAt(0).toUpperCase() + value.slice(1);
        return value;
      },
    },
    
    // React options
    react: {
      // Use Suspense for async loading
      useSuspense: true,
      
      // Bind i18n instance to component
      bindI18n: 'languageChanged',
      
      // Bind store to component
      bindI18nStore: '',
      
      // How to handle trans component defaultValue
      transEmptyNodeValue: '',
      
      // Transform the node before render
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'span'],
    },
    
    // Resource loading options
    load: 'languageOnly', // Remove region code (e.g., en-US -> en)
    
    // Preload languages
    preload: supportedLanguages,
    
    // Clean code on language change
    cleanCode: true,
    
    // Key separator (set to false to use nested keys)
    keySeparator: '.',
    
    // Namespace separator
    nsSeparator: ':',
    
    // Pluralization
    pluralSeparator: '_',
    contextSeparator: '_',
    
    // Missing key handling
    saveMissing: import.meta.env.DEV,
    missingKeyHandler: import.meta.env.DEV ? (lng, ns, key) => {
      console.warn(`Missing translation key: ${key} for language: ${lng} in namespace: ${ns}`);
    } : undefined,
    
    // Return objects for missing keys
    returnObjects: false,
    returnEmptyString: false,
    returnNull: false,
  });

export default i18n;
