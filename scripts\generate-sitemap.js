#!/usr/bin/env node

/**
 * Generate sitemap.xml and robots.txt for the website
 * Run with: node scripts/generate-sitemap.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const BASE_URL = process.env.VITE_BASE_URL || 'https://crash-events.com';
const OUTPUT_DIR = path.join(__dirname, '..', 'public');

// Supported languages
const languages = [
  { code: 'en', name: 'English' },
  { code: 'nl', name: 'Dutch' },
  { code: 'fr', name: 'French' },
];

// Pages configuration
const pages = [
  {
    path: '/',
    changefreq: 'weekly',
    priority: 1.0,
  },
  {
    path: '/agency',
    changefreq: 'monthly',
    priority: 0.8,
  },
  {
    path: '/about',
    changefreq: 'monthly',
    priority: 0.7,
  },
  {
    path: '/contact',
    changefreq: 'monthly',
    priority: 0.6,
  },
];

/**
 * Generate sitemap URLs
 */
function generateSitemapUrls() {
  const currentDate = new Date().toISOString().split('T')[0];
  const urls = [];

  pages.forEach(page => {
    languages.forEach(lang => {
      const isDefault = lang.code === 'en';
      const langPrefix = isDefault ? '' : `/${lang.code}`;
      const fullPath = page.path === '/' ? langPrefix || '/' : `${langPrefix}${page.path}`;
      
      // Generate alternate language links
      const alternates = languages.map(altLang => {
        const altIsDefault = altLang.code === 'en';
        const altLangPrefix = altIsDefault ? '' : `/${altLang.code}`;
        const altFullPath = page.path === '/' ? altLangPrefix || '/' : `${altLangPrefix}${page.path}`;
        
        return {
          hreflang: altLang.code,
          href: `${BASE_URL}${altFullPath}`,
        };
      });

      // Add x-default
      alternates.push({
        hreflang: 'x-default',
        href: `${BASE_URL}${page.path}`,
      });

      urls.push({
        loc: `${BASE_URL}${fullPath}`,
        lastmod: currentDate,
        changefreq: page.changefreq,
        priority: page.priority,
        alternates,
      });
    });
  });

  return urls;
}

/**
 * Generate XML sitemap content
 */
function generateSitemapXML() {
  const urls = generateSitemapUrls();
  
  const urlElements = urls.map(url => {
    const alternateLinks = url.alternates.map(alt => 
      `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />`
    ).join('\n');

    return `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
${alternateLinks}
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${urlElements}
</urlset>`;
}

/**
 * Generate robots.txt content
 */
function generateRobotsTxt() {
  return `User-agent: *
Allow: /

# Language-specific pages
Allow: /en/
Allow: /nl/
Allow: /fr/

# Sitemaps
Sitemap: ${BASE_URL}/sitemap.xml

# Crawl delay
Crawl-delay: 1

# Block development/admin paths
Disallow: /admin/
Disallow: /_next/
Disallow: /api/
Disallow: /private/

# Allow social media crawlers
User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /`;
}

/**
 * Main function to generate files
 */
function main() {
  try {
    console.log('🚀 Generating SEO files...');
    console.log(`📍 Base URL: ${BASE_URL}`);
    console.log(`📁 Output directory: ${OUTPUT_DIR}`);

    // Ensure output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    // Generate sitemap.xml
    const sitemapContent = generateSitemapXML();
    const sitemapPath = path.join(OUTPUT_DIR, 'sitemap.xml');
    fs.writeFileSync(sitemapPath, sitemapContent, 'utf8');
    console.log('✅ Generated sitemap.xml');

    // Generate robots.txt
    const robotsContent = generateRobotsTxt();
    const robotsPath = path.join(OUTPUT_DIR, 'robots.txt');
    fs.writeFileSync(robotsPath, robotsContent, 'utf8');
    console.log('✅ Generated robots.txt');

    // Log statistics
    const urls = generateSitemapUrls();
    console.log(`📊 Generated ${urls.length} URLs across ${languages.length} languages`);
    console.log(`🌍 Languages: ${languages.map(l => l.code).join(', ')}`);
    console.log(`📄 Pages: ${pages.length} base pages`);

    console.log('\n🎉 SEO files generated successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Submit sitemap.xml to Google Search Console');
    console.log('2. Verify robots.txt is accessible at /robots.txt');
    console.log('3. Test URLs in Google Search Console');
    console.log('4. Monitor crawl errors and index coverage');

  } catch (error) {
    console.error('❌ Error generating SEO files:', error);
    process.exit(1);
  }
}

// Run the script
main();

export {
  generateSitemapUrls,
  generateSitemapXML,
  generateRobotsTxt,
};
